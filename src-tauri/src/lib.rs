use std::fs;
use std::sync::Mutex;
use rusqlite::Connection;
use serde::{Deserialize, Serialize};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, State};
use anyhow::Result;

// Database state management
pub struct DatabaseState {
    pub connection: Mutex<Option<Connection>>,
}

// Data structures matching the Electron app
#[derive(Debug, Serialize, Deserialize)]
pub struct Part {
    pub part_id: Option<i64>,
    pub part_number: String,
    pub part_name: String,
    pub part_name_en: Option<String>,
    pub category_id: Option<i64>,
    pub description: Option<String>,
    pub purchase_price: f64,
    pub selling_price: f64,
    pub quantity: i32,
    pub min_quantity: i32,
    pub barcode: Option<String>,
    pub shelf_location: Option<String>,
    pub reorder_point: Option<i32>,
    pub preferred_supplier_id: Option<i64>,
    pub last_stock_check_date: Option<String>,
    pub is_active: bool,
    pub weight_kg: Option<f64>,
    pub dimensions_cm: Option<String>,
    pub alternative_part_numbers: Option<String>,
    pub image_path: Option<String>,
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Customer {
    pub customer_id: Option<i64>,
    pub customer_name: String,
    pub contact_person: Option<String>,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub address: Option<String>,
    pub city: Option<String>,
    pub country: Option<String>,
    pub customer_type: String,
    pub loyalty_points: i32,
    pub last_purchase_date: Option<String>,
    pub total_spent_amount: f64,
    pub credit_limit: f64,
    pub tax_id_number: Option<String>,
    pub account_manager_id: Option<i64>,
    pub notes: Option<String>,
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SalesInvoice {
    pub invoice_id: Option<i64>,
    pub customer_id: i64,
    pub invoice_number: String,
    pub invoice_date: String,
    pub total_amount: f64,
    pub discount_amount: f64,
    pub tax_amount: f64,
    pub final_amount: f64,
    pub paid_amount: f64,
    pub payment_status: String,
    pub shipping_address_details: Option<String>,
    pub shipping_method_id: Option<i64>,
    pub shipping_tracking_number: Option<String>,
    pub promotion_code_applied: Option<String>,
    pub sales_channel: String,
    pub original_invoice_id_for_return: Option<i64>,
    pub notes: Option<String>,
    pub user_id: i64,
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Debt {
    pub debt_id: Option<i64>,
    pub debt_type: String,
    pub customer_id: Option<i64>,
    pub supplier_id: Option<i64>,
    pub related_sales_invoice_id: Option<i64>,
    pub related_purchase_invoice_id: Option<i64>,
    pub debt_reference_number: Option<String>,
    pub debt_description: Option<String>,
    pub principal_amount: f64,
    pub currency_code: String,
    pub interest_rate_annual_percent: f64,
    pub interest_calculation_method: String,
    pub compounding_frequency: String,
    pub interest_accrued: f64,
    pub total_debt_amount_due: f64,
    pub amount_paid: f64,
    pub remaining_balance: f64,
    pub issue_date: String,
    pub due_date: String,
    pub payment_terms_details: Option<String>,
    pub status: String,
    pub last_payment_date: Option<String>,
    pub user_id_created: Option<i64>,
    pub notes: Option<String>,
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
}

// Database initialization
#[tauri::command]
async fn initialize_database(app_handle: AppHandle) -> Result<String, String> {
    let app_dir = app_handle.path().app_data_dir()
        .map_err(|e| format!("Failed to get app data directory: {}", e))?;

    // Create database directory if it doesn't exist
    let db_dir = app_dir.join("database");
    fs::create_dir_all(&db_dir)
        .map_err(|e| format!("Failed to create database directory: {}", e))?;

    let db_path = db_dir.join("truck_parts.db");

    // Open database connection
    let conn = Connection::open(&db_path)
        .map_err(|e| format!("Failed to open database: {}", e))?;

    // Enable foreign keys
    conn.execute("PRAGMA foreign_keys = ON", [])
        .map_err(|e| format!("Failed to enable foreign keys: {}", e))?;

    // Check if tables exist, if not create them
    let table_count: i64 = conn.query_row(
        "SELECT COUNT(*) FROM sqlite_master WHERE type='table'",
        [],
        |row| row.get(0)
    ).map_err(|e| format!("Failed to check tables: {}", e))?;

    if table_count == 0 {
        create_database_schema(&conn)?;
    }

    // Store connection in state
    let state: State<DatabaseState> = app_handle.state();
    let mut db_conn = state.connection.lock().unwrap();
    *db_conn = Some(conn);

    Ok("Database initialized successfully".to_string())
}

// Create database schema
fn create_database_schema(conn: &Connection) -> Result<(), String> {
    let schema = r#"
        -- Categories table
        CREATE TABLE IF NOT EXISTS categories (
            category_id INTEGER PRIMARY KEY AUTOINCREMENT,
            category_name TEXT NOT NULL UNIQUE,
            category_name_en TEXT,
            description TEXT,
            parent_category_id INTEGER,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_category_id) REFERENCES categories(category_id)
        );

        -- Parts table
        CREATE TABLE IF NOT EXISTS parts (
            part_id INTEGER PRIMARY KEY AUTOINCREMENT,
            part_number TEXT NOT NULL UNIQUE,
            part_name TEXT NOT NULL,
            part_name_en TEXT,
            category_id INTEGER,
            description TEXT,
            purchase_price REAL NOT NULL DEFAULT 0,
            selling_price REAL NOT NULL DEFAULT 0,
            quantity INTEGER NOT NULL DEFAULT 0,
            min_quantity INTEGER NOT NULL DEFAULT 0,
            barcode TEXT UNIQUE,
            shelf_location TEXT,
            reorder_point INTEGER,
            preferred_supplier_id INTEGER,
            last_stock_check_date DATE,
            is_active BOOLEAN DEFAULT 1,
            weight_kg REAL,
            dimensions_cm TEXT,
            alternative_part_numbers TEXT,
            image_path TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(category_id)
        );

        -- Customers table
        CREATE TABLE IF NOT EXISTS customers (
            customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_name TEXT NOT NULL,
            contact_person TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            city TEXT,
            country TEXT,
            customer_type TEXT CHECK(customer_type IN ('individual', 'company', 'workshop', 'fleet_owner')) DEFAULT 'individual',
            loyalty_points INTEGER DEFAULT 0,
            last_purchase_date DATE,
            total_spent_amount REAL DEFAULT 0,
            credit_limit REAL DEFAULT 0,
            tax_id_number TEXT,
            account_manager_id INTEGER,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        -- Sales invoices table
        CREATE TABLE IF NOT EXISTS sales_invoices (
            invoice_id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER NOT NULL,
            invoice_number TEXT NOT NULL UNIQUE,
            invoice_date DATE NOT NULL,
            total_amount REAL NOT NULL DEFAULT 0,
            discount_amount REAL DEFAULT 0,
            tax_amount REAL DEFAULT 0,
            final_amount REAL NOT NULL DEFAULT 0,
            paid_amount REAL DEFAULT 0,
            payment_status TEXT CHECK(payment_status IN ('paid', 'partial', 'unpaid')) DEFAULT 'unpaid',
            shipping_address_details TEXT,
            shipping_method_id INTEGER,
            shipping_tracking_number TEXT,
            promotion_code_applied TEXT,
            sales_channel TEXT CHECK(sales_channel IN ('in_store', 'online_website', 'phone_order', 'social_media')) DEFAULT 'in_store',
            original_invoice_id_for_return INTEGER,
            notes TEXT,
            user_id INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
            FOREIGN KEY (original_invoice_id_for_return) REFERENCES sales_invoices(invoice_id)
        );

        -- Debts table
        CREATE TABLE IF NOT EXISTS Debts (
            debt_id INTEGER PRIMARY KEY AUTOINCREMENT,
            debt_type TEXT CHECK(debt_type IN ('receivable', 'payable')) NOT NULL,
            customer_id INTEGER,
            supplier_id INTEGER,
            related_sales_invoice_id INTEGER,
            related_purchase_invoice_id INTEGER,
            debt_reference_number TEXT,
            debt_description TEXT,
            principal_amount REAL NOT NULL DEFAULT 0,
            currency_code TEXT DEFAULT 'USD',
            interest_rate_annual_percent REAL DEFAULT 0,
            interest_calculation_method TEXT CHECK(interest_calculation_method IN ('simple', 'compound', 'none')) DEFAULT 'none',
            compounding_frequency TEXT CHECK(compounding_frequency IN ('daily', 'monthly', 'quarterly', 'annually', 'none')) DEFAULT 'none',
            interest_accrued REAL DEFAULT 0,
            total_debt_amount_due REAL NOT NULL DEFAULT 0,
            amount_paid REAL DEFAULT 0,
            remaining_balance REAL NOT NULL DEFAULT 0,
            issue_date DATE NOT NULL,
            due_date DATE NOT NULL,
            payment_terms_details TEXT,
            status TEXT CHECK(status IN ('pending_approval', 'active', 'partially_paid', 'fully_paid', 'overdue', 'disputed', 'written_off', 'cancelled')) DEFAULT 'active',
            last_payment_date DATE,
            user_id_created INTEGER,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
            FOREIGN KEY (related_sales_invoice_id) REFERENCES sales_invoices(invoice_id)
        );

        -- Create indexes for better performance
        CREATE INDEX IF NOT EXISTS idx_parts_category ON parts(category_id);
        CREATE INDEX IF NOT EXISTS idx_parts_barcode ON parts(barcode);
        CREATE INDEX IF NOT EXISTS idx_parts_active ON parts(is_active);
        CREATE INDEX IF NOT EXISTS idx_sales_invoices_customer ON sales_invoices(customer_id);
        CREATE INDEX IF NOT EXISTS idx_sales_invoices_date ON sales_invoices(invoice_date);
        CREATE INDEX IF NOT EXISTS idx_debts_customer ON Debts(customer_id);
        CREATE INDEX IF NOT EXISTS idx_debts_status ON Debts(status);
    "#;

    conn.execute_batch(schema)
        .map_err(|e| format!("Failed to create database schema: {}", e))?;

    Ok(())
}

// Parts operations
#[tauri::command]
async fn get_all_parts(app_handle: AppHandle) -> Result<Vec<Part>, String> {
    let state: State<DatabaseState> = app_handle.state();
    let db_conn = state.connection.lock().unwrap();

    if let Some(ref conn) = *db_conn {
        let mut stmt = conn.prepare(
            "SELECT part_id, part_number, part_name, part_name_en, category_id, description,
             purchase_price, selling_price, quantity, min_quantity, barcode, shelf_location,
             reorder_point, preferred_supplier_id, last_stock_check_date, is_active,
             weight_kg, dimensions_cm, alternative_part_numbers, image_path,
             created_at, updated_at FROM parts WHERE is_active = 1 ORDER BY part_name ASC"
        ).map_err(|e| format!("Failed to prepare statement: {}", e))?;

        let part_iter = stmt.query_map([], |row| {
            Ok(Part {
                part_id: row.get(0)?,
                part_number: row.get(1)?,
                part_name: row.get(2)?,
                part_name_en: row.get(3)?,
                category_id: row.get(4)?,
                description: row.get(5)?,
                purchase_price: row.get(6)?,
                selling_price: row.get(7)?,
                quantity: row.get(8)?,
                min_quantity: row.get(9)?,
                barcode: row.get(10)?,
                shelf_location: row.get(11)?,
                reorder_point: row.get(12)?,
                preferred_supplier_id: row.get(13)?,
                last_stock_check_date: row.get(14)?,
                is_active: row.get(15)?,
                weight_kg: row.get(16)?,
                dimensions_cm: row.get(17)?,
                alternative_part_numbers: row.get(18)?,
                image_path: row.get(19)?,
                created_at: row.get(20)?,
                updated_at: row.get(21)?,
            })
        }).map_err(|e| format!("Failed to query parts: {}", e))?;

        let mut parts = Vec::new();
        for part in part_iter {
            parts.push(part.map_err(|e| format!("Failed to parse part: {}", e))?);
        }

        Ok(parts)
    } else {
        Err("Database not initialized".to_string())
    }
}

#[tauri::command]
async fn search_parts(app_handle: AppHandle, search_term: String) -> Result<Vec<Part>, String> {
    let state: State<DatabaseState> = app_handle.state();
    let db_conn = state.connection.lock().unwrap();

    if let Some(ref conn) = *db_conn {
        let search_pattern = format!("%{}%", search_term);
        let mut stmt = conn.prepare(
            "SELECT part_id, part_number, part_name, part_name_en, category_id, description,
             purchase_price, selling_price, quantity, min_quantity, barcode, shelf_location,
             reorder_point, preferred_supplier_id, last_stock_check_date, is_active,
             weight_kg, dimensions_cm, alternative_part_numbers, image_path,
             created_at, updated_at FROM parts
             WHERE is_active = 1 AND (part_name LIKE ?1 OR part_number LIKE ?1 OR barcode LIKE ?1 OR description LIKE ?1)
             ORDER BY part_name ASC"
        ).map_err(|e| format!("Failed to prepare statement: {}", e))?;

        let part_iter = stmt.query_map([&search_pattern], |row| {
            Ok(Part {
                part_id: row.get(0)?,
                part_number: row.get(1)?,
                part_name: row.get(2)?,
                part_name_en: row.get(3)?,
                category_id: row.get(4)?,
                description: row.get(5)?,
                purchase_price: row.get(6)?,
                selling_price: row.get(7)?,
                quantity: row.get(8)?,
                min_quantity: row.get(9)?,
                barcode: row.get(10)?,
                shelf_location: row.get(11)?,
                reorder_point: row.get(12)?,
                preferred_supplier_id: row.get(13)?,
                last_stock_check_date: row.get(14)?,
                is_active: row.get(15)?,
                weight_kg: row.get(16)?,
                dimensions_cm: row.get(17)?,
                alternative_part_numbers: row.get(18)?,
                image_path: row.get(19)?,
                created_at: row.get(20)?,
                updated_at: row.get(21)?,
            })
        }).map_err(|e| format!("Failed to query parts: {}", e))?;

        let mut parts = Vec::new();
        for part in part_iter {
            parts.push(part.map_err(|e| format!("Failed to parse part: {}", e))?);
        }

        Ok(parts)
    } else {
        Err("Database not initialized".to_string())
    }
}

#[tauri::command]
async fn create_part(app_handle: AppHandle, part: Part) -> Result<i64, String> {
    let state: State<DatabaseState> = app_handle.state();
    let db_conn = state.connection.lock().unwrap();

    if let Some(ref conn) = *db_conn {
        let _result = conn.execute(
            "INSERT INTO parts (part_number, part_name, part_name_en, category_id, description,
             purchase_price, selling_price, quantity, min_quantity, barcode, shelf_location,
             reorder_point, preferred_supplier_id, is_active, weight_kg, dimensions_cm,
             alternative_part_numbers, image_path)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14, ?15, ?16, ?17, ?18)",
            rusqlite::params![
                part.part_number,
                part.part_name,
                part.part_name_en,
                part.category_id,
                part.description,
                part.purchase_price,
                part.selling_price,
                part.quantity,
                part.min_quantity,
                part.barcode,
                part.shelf_location,
                part.reorder_point,
                part.preferred_supplier_id,
                part.is_active,
                part.weight_kg,
                part.dimensions_cm,
                part.alternative_part_numbers,
                part.image_path,
            ],
        ).map_err(|e| format!("Failed to insert part: {}", e))?;

        Ok(conn.last_insert_rowid())
    } else {
        Err("Database not initialized".to_string())
    }
}

// Customer operations
#[tauri::command]
async fn get_all_customers(app_handle: AppHandle) -> Result<Vec<Customer>, String> {
    let state: State<DatabaseState> = app_handle.state();
    let db_conn = state.connection.lock().unwrap();

    if let Some(ref conn) = *db_conn {
        let mut stmt = conn.prepare(
            "SELECT customer_id, customer_name, contact_person, phone, email, address, city, country,
             customer_type, loyalty_points, last_purchase_date, total_spent_amount, credit_limit,
             tax_id_number, account_manager_id, notes, created_at, updated_at
             FROM customers ORDER BY customer_name ASC"
        ).map_err(|e| format!("Failed to prepare statement: {}", e))?;

        let customer_iter = stmt.query_map([], |row| {
            Ok(Customer {
                customer_id: row.get(0)?,
                customer_name: row.get(1)?,
                contact_person: row.get(2)?,
                phone: row.get(3)?,
                email: row.get(4)?,
                address: row.get(5)?,
                city: row.get(6)?,
                country: row.get(7)?,
                customer_type: row.get(8)?,
                loyalty_points: row.get(9)?,
                last_purchase_date: row.get(10)?,
                total_spent_amount: row.get(11)?,
                credit_limit: row.get(12)?,
                tax_id_number: row.get(13)?,
                account_manager_id: row.get(14)?,
                notes: row.get(15)?,
                created_at: row.get(16)?,
                updated_at: row.get(17)?,
            })
        }).map_err(|e| format!("Failed to query customers: {}", e))?;

        let mut customers = Vec::new();
        for customer in customer_iter {
            customers.push(customer.map_err(|e| format!("Failed to parse customer: {}", e))?);
        }

        Ok(customers)
    } else {
        Err("Database not initialized".to_string())
    }
}

#[tauri::command]
async fn create_customer(app_handle: AppHandle, customer: Customer) -> Result<i64, String> {
    let state: State<DatabaseState> = app_handle.state();
    let db_conn = state.connection.lock().unwrap();

    if let Some(ref conn) = *db_conn {
        let _result = conn.execute(
            "INSERT INTO customers (customer_name, contact_person, phone, email, address, city, country,
             customer_type, loyalty_points, last_purchase_date, total_spent_amount, credit_limit,
             tax_id_number, account_manager_id, notes)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14, ?15)",
            rusqlite::params![
                customer.customer_name,
                customer.contact_person,
                customer.phone,
                customer.email,
                customer.address,
                customer.city,
                customer.country,
                customer.customer_type,
                customer.loyalty_points,
                customer.last_purchase_date,
                customer.total_spent_amount,
                customer.credit_limit,
                customer.tax_id_number,
                customer.account_manager_id,
                customer.notes,
            ],
        ).map_err(|e| format!("Failed to insert customer: {}", e))?;

        Ok(conn.last_insert_rowid())
    } else {
        Err("Database not initialized".to_string())
    }
}

// File operations for backup/restore
#[tauri::command]
async fn create_backup(app_handle: AppHandle) -> Result<String, String> {
    let app_dir = app_handle.path().app_data_dir()
        .map_err(|e| format!("Failed to get app data directory: {}", e))?;

    let db_path = app_dir.join("database").join("truck_parts.db");

    if !db_path.exists() {
        return Err("Database file not found".to_string());
    }

    let backup_name = format!("backup_{}.db", chrono::Utc::now().format("%Y-%m-%d_%H-%M-%S"));
    let backup_path = app_dir.join("backups").join(&backup_name);

    // Create backups directory if it doesn't exist
    if let Some(parent) = backup_path.parent() {
        fs::create_dir_all(parent)
            .map_err(|e| format!("Failed to create backup directory: {}", e))?;
    }

    fs::copy(&db_path, &backup_path)
        .map_err(|e| format!("Failed to create backup: {}", e))?;

    Ok(format!("Backup created successfully at: {}", backup_path.display()))
}

#[tauri::command]
async fn restore_backup(app_handle: AppHandle) -> Result<String, String> {
    let app_dir = app_handle.path().app_data_dir()
        .map_err(|e| format!("Failed to get app data directory: {}", e))?;

    let db_path = app_dir.join("database").join("truck_parts.db");
    let backups_dir = app_dir.join("backups");

    if !backups_dir.exists() {
        return Err("No backups directory found".to_string());
    }

    // For now, restore the most recent backup
    let mut backup_files: Vec<_> = fs::read_dir(&backups_dir)
        .map_err(|e| format!("Failed to read backups directory: {}", e))?
        .filter_map(|entry| {
            let entry = entry.ok()?;
            let path = entry.path();
            if path.extension()?.to_str()? == "db" {
                Some(path)
            } else {
                None
            }
        })
        .collect();

    if backup_files.is_empty() {
        return Err("No backup files found".to_string());
    }

    backup_files.sort_by(|a, b| {
        let a_metadata = fs::metadata(a).ok();
        let b_metadata = fs::metadata(b).ok();
        match (a_metadata, b_metadata) {
            (Some(a_meta), Some(b_meta)) => b_meta.modified().unwrap_or(std::time::UNIX_EPOCH)
                .cmp(&a_meta.modified().unwrap_or(std::time::UNIX_EPOCH)),
            _ => std::cmp::Ordering::Equal,
        }
    });

    let latest_backup = &backup_files[0];

    // Close current database connection
    let state: State<DatabaseState> = app_handle.state();
    {
        let mut db_conn = state.connection.lock().unwrap();
        *db_conn = None;
    }

    // Copy backup file to database location
    fs::copy(latest_backup, &db_path)
        .map_err(|e| format!("Failed to restore backup: {}", e))?;

    // Reinitialize database
    initialize_database(app_handle).await?;

    Ok(format!("Backup restored successfully from: {}", latest_backup.display()))
}

// Generic database query function
#[tauri::command]
async fn db_query(app_handle: AppHandle, sql: String, params: Vec<serde_json::Value>) -> Result<Vec<serde_json::Value>, String> {
    let state: State<DatabaseState> = app_handle.state();
    let db_conn = state.connection.lock().unwrap();

    if let Some(ref conn) = *db_conn {
        let mut stmt = conn.prepare(&sql)
            .map_err(|e| format!("Failed to prepare statement: {}", e))?;

        // Convert JSON values to rusqlite parameters
        let rusqlite_params: Vec<rusqlite::types::Value> = params.into_iter().map(|v| {
            match v {
                serde_json::Value::String(s) => rusqlite::types::Value::Text(s),
                serde_json::Value::Number(n) => {
                    if let Some(i) = n.as_i64() {
                        rusqlite::types::Value::Integer(i)
                    } else if let Some(f) = n.as_f64() {
                        rusqlite::types::Value::Real(f)
                    } else {
                        rusqlite::types::Value::Null
                    }
                },
                serde_json::Value::Bool(b) => rusqlite::types::Value::Integer(if b { 1 } else { 0 }),
                serde_json::Value::Null => rusqlite::types::Value::Null,
                _ => rusqlite::types::Value::Null,
            }
        }).collect();

        let rows = stmt.query_map(rusqlite::params_from_iter(rusqlite_params.iter()), |row| {
            let mut result = serde_json::Map::new();
            let column_count = row.as_ref().column_count();

            for i in 0..column_count {
                let default_name = format!("col_{}", i);
                let column_name = row.as_ref().column_name(i).unwrap_or(&default_name);
                let value: rusqlite::types::Value = row.get(i)?;

                let json_value = match value {
                    rusqlite::types::Value::Null => serde_json::Value::Null,
                    rusqlite::types::Value::Integer(i) => serde_json::Value::Number(serde_json::Number::from(i)),
                    rusqlite::types::Value::Real(f) => serde_json::Value::Number(serde_json::Number::from_f64(f).unwrap_or(serde_json::Number::from(0))),
                    rusqlite::types::Value::Text(s) => serde_json::Value::String(s),
                    rusqlite::types::Value::Blob(_) => serde_json::Value::Null, // Skip blob data
                };

                result.insert(column_name.to_string(), json_value);
            }

            Ok(serde_json::Value::Object(result))
        }).map_err(|e| format!("Failed to execute query: {}", e))?;

        let mut results = Vec::new();
        for row in rows {
            results.push(row.map_err(|e| format!("Failed to parse row: {}", e))?);
        }

        Ok(results)
    } else {
        Err("Database not initialized".to_string())
    }
}

// Generic database run function (for INSERT, UPDATE, DELETE)
#[tauri::command]
async fn db_run(app_handle: AppHandle, sql: String, params: Vec<serde_json::Value>) -> Result<serde_json::Value, String> {
    let state: State<DatabaseState> = app_handle.state();
    let db_conn = state.connection.lock().unwrap();

    if let Some(ref conn) = *db_conn {
        // Convert JSON values to rusqlite parameters
        let rusqlite_params: Vec<rusqlite::types::Value> = params.into_iter().map(|v| {
            match v {
                serde_json::Value::String(s) => rusqlite::types::Value::Text(s),
                serde_json::Value::Number(n) => {
                    if let Some(i) = n.as_i64() {
                        rusqlite::types::Value::Integer(i)
                    } else if let Some(f) = n.as_f64() {
                        rusqlite::types::Value::Real(f)
                    } else {
                        rusqlite::types::Value::Null
                    }
                },
                serde_json::Value::Bool(b) => rusqlite::types::Value::Integer(if b { 1 } else { 0 }),
                serde_json::Value::Null => rusqlite::types::Value::Null,
                _ => rusqlite::types::Value::Null,
            }
        }).collect();

        let changes = conn.execute(&sql, rusqlite::params_from_iter(rusqlite_params.iter()))
            .map_err(|e| format!("Failed to execute statement: {}", e))?;

        let last_id = conn.last_insert_rowid();

        let mut result = serde_json::Map::new();
        result.insert("lastID".to_string(), serde_json::Value::Number(serde_json::Number::from(last_id)));
        result.insert("changes".to_string(), serde_json::Value::Number(serde_json::Number::from(changes)));

        Ok(serde_json::Value::Object(result))
    } else {
        Err("Database not initialized".to_string())
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        .manage(DatabaseState {
            connection: Mutex::new(None),
        })
        .invoke_handler(tauri::generate_handler![
            initialize_database,
            get_all_parts,
            search_parts,
            create_part,
            get_all_customers,
            create_customer,
            create_backup,
            restore_backup,
            db_query,
            db_run
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
