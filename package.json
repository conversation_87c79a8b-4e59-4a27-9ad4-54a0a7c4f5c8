{"name": "truck-parts-manager-ta<PERSON>", "private": true, "version": "1.0.0", "description": "تطبيق إدارة متجر قطع غيار الشاحنات - Truck Parts Store Management Application", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}, "keywords": ["truck", "parts", "inventory", "management", "arabic", "tauri", "react"], "author": "Truck Parts Manager", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-dialog": "^2", "@tauri-apps/plugin-fs": "^2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.2", "@mui/material": "^5.15.2", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.2", "dayjs": "^1.11.10", "i18next": "^23.7.6", "react-i18next": "^13.5.0", "react-router-dom": "^6.20.1", "recharts": "^2.15.3"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "typescript": "^5.2.2", "vite": "^5.0.8", "@tauri-apps/cli": "^2"}}