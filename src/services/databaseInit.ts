// Database Initialization Service for Tauri
import { invoke } from '@tauri-apps/api/core';

export class DatabaseInitService {
  private static instance: DatabaseInitService;

  private constructor() {}

  public static getInstance(): DatabaseInitService {
    if (!DatabaseInitService.instance) {
      DatabaseInitService.instance = new DatabaseInitService();
    }
    return DatabaseInitService.instance;
  }

  async initializeDatabase(): Promise<void> {
    try {
      await invoke('initialize_database');
      console.log('Tauri database initialized successfully');
    } catch (error) {
      console.error('Error initializing Tauri database:', error);
      throw error;
    }
  }
}

export const dbInitService = DatabaseInitService.getInstance();
