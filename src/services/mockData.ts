// Mock Data Service for Browser Mode
// خدمة البيانات الوهمية لوضع المتصفح

export const mockCategories = [
  { category_id: 1, category_name: 'محرك', category_name_en: 'Engine', description: 'قطع غيار المحرك وملحقاته', display_order: 1 },
  { category_id: 2, category_name: 'فرامل', category_name_en: 'Brakes', description: 'نظام الفرامل والأقراص والأحذية', display_order: 2 },
  { category_id: 3, category_name: 'إطارات', category_name_en: 'Tires', description: 'الإطارات والعجلات والجنوط', display_order: 3 },
  { category_id: 4, category_name: 'كهرباء', category_name_en: 'Electrical', description: 'النظام الكهربائي والبطاريات', display_order: 4 },
  { category_id: 5, category_name: 'تكييف', category_name_en: 'Air Conditioning', description: 'نظام التكييف والتبريد', display_order: 5 },
];

export const mockParts = [
  {
    part_id: 1,
    part_number: 'ENG001',
    part_name: 'فلتر زيت محرك',
    part_name_en: 'Engine Oil Filter',
    category_id: 1,
    description: 'فلتر زيت عالي الجودة للمحركات الثقيلة',
    purchase_price: 1500,
    selling_price: 2000,
    quantity: 25,
    min_quantity: 10,
    barcode: '*********0123',
    shelf_location: 'A1-01',
    reorder_point: 15,
    preferred_supplier_id: 1,
    is_active: true,
    weight_kg: 0.5,
    dimensions_cm: '10x10x15',
    alternative_part_numbers: 'ALT001, ALT002',
    image_path: null,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    part_id: 2,
    part_number: 'BRK001',
    part_name: 'أقراص فرامل أمامية',
    part_name_en: 'Front Brake Discs',
    category_id: 2,
    description: 'أقراص فرامل عالية الأداء للشاحنات',
    purchase_price: 3000,
    selling_price: 4000,
    quantity: 15,
    min_quantity: 8,
    barcode: '2345678901234',
    shelf_location: 'B2-01',
    reorder_point: 10,
    preferred_supplier_id: 2,
    is_active: true,
    weight_kg: 8.5,
    dimensions_cm: '35x35x5',
    alternative_part_numbers: 'BRK002, BRK003',
    image_path: null,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    part_id: 3,
    part_number: 'TIR001',
    part_name: 'إطار 315/80R22.5',
    part_name_en: 'Tire 315/80R22.5',
    category_id: 3,
    description: 'إطار للشاحنات الثقيلة مقاوم للتآكل',
    purchase_price: 25000,
    selling_price: 32000,
    quantity: 12,
    min_quantity: 5,
    barcode: '3456789012345',
    shelf_location: 'C1-01',
    reorder_point: 8,
    preferred_supplier_id: 3,
    is_active: true,
    weight_kg: 65.0,
    dimensions_cm: '100x100x30',
    alternative_part_numbers: 'TIR002, TIR003',
    image_path: null,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

export const mockCustomers = [
  {
    customer_id: 1,
    customer_name: 'أحمد محمد علي',
    contact_person: null,
    phone: '0*********',
    email: '<EMAIL>',
    address: 'شارع الاستقلال 123',
    city: 'الجزائر',
    country: 'الجزائر',
    customer_type: 'individual' as const,
    loyalty_points: 150,
    last_purchase_date: '2024-05-29',
    total_spent_amount: 125000,
    credit_limit: 30000,
    tax_id_number: null,
    account_manager_id: null,
    notes: 'عميل مميز، يشتري بانتظام',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    customer_id: 2,
    customer_name: 'شركة النقل السريع',
    contact_person: 'محمد علي',
    phone: '**********',
    email: '<EMAIL>',
    address: 'المنطقة الصناعية الأولى',
    city: 'وهران',
    country: 'الجزائر',
    customer_type: 'company' as const,
    loyalty_points: 500,
    last_purchase_date: '2024-05-28',
    total_spent_amount: 850000,
    credit_limit: 200000,
    tax_id_number: '*********',
    account_manager_id: 1,
    notes: 'شركة نقل كبيرة، تحتاج قطع غيار بكميات كبيرة',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

export const mockSalesInvoices = [
  {
    invoice_id: 1,
    customer_id: 1,
    invoice_number: 'INV-2024-001',
    invoice_date: '2024-05-29',
    total_amount: 32000,
    discount_amount: 0,
    tax_amount: 0,
    final_amount: 32000,
    paid_amount: 32000,
    payment_status: 'paid' as const,
    shipping_address_details: null,
    shipping_method_id: null,
    shipping_tracking_number: null,
    promotion_code_applied: null,
    sales_channel: 'in_store' as const,
    original_invoice_id_for_return: null,
    notes: 'فاتورة مدفوعة بالكامل',
    user_id: 1,
    created_at: '2024-05-29T00:00:00Z',
    updated_at: '2024-05-29T00:00:00Z'
  },
  {
    invoice_id: 2,
    customer_id: 2,
    invoice_number: 'INV-2024-002',
    invoice_date: '2024-05-28',
    total_amount: 8000,
    discount_amount: 200,
    tax_amount: 0,
    final_amount: 7800,
    paid_amount: 0,
    payment_status: 'unpaid' as const,
    shipping_address_details: null,
    shipping_method_id: null,
    shipping_tracking_number: null,
    promotion_code_applied: null,
    sales_channel: 'phone_order' as const,
    original_invoice_id_for_return: null,
    notes: 'في انتظار الدفع',
    user_id: 1,
    created_at: '2024-05-28T00:00:00Z',
    updated_at: '2024-05-28T00:00:00Z'
  }
];

export const mockDebts = [
  {
    debt_id: 1,
    debt_type: 'receivable' as const,
    customer_id: 2,
    supplier_id: null,
    related_sales_invoice_id: 2,
    related_purchase_invoice_id: null,
    debt_reference_number: 'DEBT-2024-001',
    debt_description: 'دين من فاتورة INV-2024-002',
    principal_amount: 7800,
    currency_code: 'DZD',
    interest_rate_annual_percent: 0,
    interest_calculation_method: 'none' as const,
    compounding_frequency: 'none' as const,
    interest_accrued: 0,
    total_debt_amount_due: 7800,
    amount_paid: 0,
    remaining_balance: 7800,
    issue_date: '2024-05-28',
    due_date: '2024-06-15',
    payment_terms_details: 'دفع خلال 30 يوم',
    status: 'active' as const,
    last_payment_date: null,
    user_id_created: 1,
    notes: 'دين جديد',
    created_at: '2024-05-28T00:00:00Z',
    updated_at: '2024-05-28T00:00:00Z'
  }
];

// Mock Dashboard Stats
export const mockDashboardStats = {
  totalParts: mockParts.length,
  totalCustomers: mockCustomers.length,
  lowStockItems: mockParts.filter(p => p.quantity <= p.min_quantity).length,
  totalSalesToday: mockSalesInvoices
    .filter(inv => inv.invoice_date === new Date().toISOString().split('T')[0])
    .reduce((sum, inv) => sum + inv.final_amount, 0),
  totalDebts: mockDebts
    .filter(debt => debt.debt_type === 'receivable' && ['active', 'partially_paid', 'overdue'].includes(debt.status))
    .reduce((sum, debt) => sum + debt.remaining_balance, 0),
  overdueDebts: mockDebts
    .filter(debt => debt.debt_type === 'receivable' && (debt.status === 'overdue' || (debt.due_date < new Date().toISOString().split('T')[0] && ['active', 'partially_paid'].includes(debt.status))))
    .length
};
