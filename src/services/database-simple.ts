// Simplified Database Service for Tauri Migration
import { invoke } from '@tauri-apps/api/core';
import { mockParts, mockCustomers, mockSalesInvoices, mockDebts, mockCategories, mockDashboardStats } from './mockData';

// Import types from the original database service
export interface Part {
  part_id: number;
  part_number: string;
  part_name: string;
  part_name_en?: string;
  category_id?: number;
  description?: string;
  purchase_price: number;
  selling_price: number;
  quantity: number;
  min_quantity: number;
  barcode?: string;
  shelf_location?: string;
  reorder_point?: number;
  preferred_supplier_id?: number;
  last_stock_check_date?: string;
  is_active: boolean;
  weight_kg?: number;
  dimensions_cm?: string;
  alternative_part_numbers?: string;
  image_path?: string | null;
  created_at?: string;
  updated_at?: string;
}

export interface Customer {
  customer_id: number;
  customer_name: string;
  contact_person?: string | null;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  country?: string;
  customer_type: string;
  loyalty_points: number;
  last_purchase_date?: string;
  total_spent_amount: number;
  credit_limit: number;
  tax_id_number?: string | null;
  account_manager_id?: number | null;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface SalesInvoice {
  invoice_id: number;
  customer_id: number;
  invoice_number: string;
  invoice_date: string;
  total_amount: number;
  discount_amount: number;
  tax_amount: number;
  final_amount: number;
  paid_amount: number;
  payment_status: string;
  shipping_address_details?: string | null;
  shipping_method_id?: number | null;
  shipping_tracking_number?: string | null;
  promotion_code_applied?: string | null;
  sales_channel: string;
  original_invoice_id_for_return?: number | null;
  notes?: string;
  user_id: number;
  created_at?: string;
  updated_at?: string;
}

export interface Debt {
  debt_id?: number;
  debt_type: string;
  customer_id?: number;
  supplier_id?: number | null;
  related_sales_invoice_id?: number | null;
  related_purchase_invoice_id?: number | null;
  debt_reference_number?: string;
  debt_description?: string;
  principal_amount: number;
  currency_code: string;
  interest_rate_annual_percent: number;
  interest_calculation_method: string;
  compounding_frequency: string;
  interest_accrued: number;
  total_debt_amount_due: number;
  amount_paid: number;
  remaining_balance: number;
  issue_date: string;
  due_date: string;
  payment_terms_details?: string;
  status: string;
  last_payment_date?: string | null;
  user_id_created?: number;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export class DatabaseService {
  private static instance: DatabaseService;
  private isInitialized: boolean = false;

  // Local storage for fallback mode
  private localParts: Part[] = [...mockParts];
  private localCustomers: Customer[] = [...mockCustomers];
  private localSalesInvoices: SalesInvoice[] = [...mockSalesInvoices];
  private localDebts: Debt[] = [...mockDebts];

  private constructor() {
    this.initializeDatabase();
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  private async initializeDatabase(): Promise<void> {
    try {
      await invoke('initialize_database');
      this.isInitialized = true;
      console.log('Tauri database initialized successfully');
    } catch (error) {
      console.warn('Failed to initialize Tauri database, using local storage:', error);
      this.isInitialized = false;
    }
  }

  // Parts operations
  async getAllParts(): Promise<Part[]> {
    if (!this.isInitialized) {
      return this.localParts.filter(part => part.is_active);
    }

    try {
      return await invoke('get_all_parts');
    } catch (error) {
      console.error('Failed to get parts from Tauri:', error);
      return this.localParts.filter(part => part.is_active);
    }
  }

  async searchParts(searchTerm: string): Promise<Part[]> {
    if (!this.isInitialized) {
      const term = searchTerm.toLowerCase();
      return this.localParts.filter(part => 
        part.is_active && (
          part.part_name.toLowerCase().includes(term) ||
          part.part_number.toLowerCase().includes(term) ||
          (part.barcode && part.barcode.toLowerCase().includes(term)) ||
          (part.description && part.description.toLowerCase().includes(term))
        )
      );
    }

    try {
      return await invoke('search_parts', { searchTerm });
    } catch (error) {
      console.error('Failed to search parts from Tauri:', error);
      return [];
    }
  }

  async createPart(part: Omit<Part, 'part_id' | 'created_at' | 'updated_at'>): Promise<number> {
    if (!this.isInitialized) {
      const newPart: Part = {
        ...part,
        part_id: Date.now(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      this.localParts.push(newPart);
      return newPart.part_id;
    }

    try {
      return await invoke('create_part', { part });
    } catch (error) {
      console.error('Failed to create part in Tauri:', error);
      throw error;
    }
  }

  // Customer operations
  async getAllCustomers(): Promise<Customer[]> {
    if (!this.isInitialized) {
      return this.localCustomers;
    }

    try {
      return await invoke('get_all_customers');
    } catch (error) {
      console.error('Failed to get customers from Tauri:', error);
      return this.localCustomers;
    }
  }

  async createCustomer(customer: Omit<Customer, 'customer_id' | 'created_at' | 'updated_at'>): Promise<number> {
    if (!this.isInitialized) {
      const newCustomer: Customer = {
        ...customer,
        customer_id: Date.now(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      this.localCustomers.push(newCustomer);
      return newCustomer.customer_id;
    }

    try {
      return await invoke('create_customer', { customer });
    } catch (error) {
      console.error('Failed to create customer in Tauri:', error);
      throw error;
    }
  }

  // Sales operations
  async getAllSalesInvoices(): Promise<SalesInvoice[]> {
    return this.localSalesInvoices;
  }

  // Debt operations
  async getAllDebts(): Promise<Debt[]> {
    return this.localDebts;
  }

  // Categories operations
  async getAllCategories(): Promise<any[]> {
    return mockCategories;
  }

  // Dashboard statistics
  async getDashboardStats(): Promise<any> {
    return mockDashboardStats;
  }

  // Backup and restore
  async createBackup(): Promise<string> {
    if (!this.isInitialized) {
      throw new Error('Database not initialized');
    }

    try {
      return await invoke('create_backup');
    } catch (error) {
      console.error('Failed to create backup:', error);
      throw error;
    }
  }

  async restoreBackup(): Promise<string> {
    if (!this.isInitialized) {
      throw new Error('Database not initialized');
    }

    try {
      const result = await invoke('restore_backup');
      await this.initializeDatabase();
      return result;
    } catch (error) {
      console.error('Failed to restore backup:', error);
      throw error;
    }
  }

  // Utility methods
  async executeQuery(sql: string, params: any[] = []): Promise<any[]> {
    if (!this.isInitialized) {
      return [];
    }
    
    try {
      return await invoke('db_query', { sql, params });
    } catch (error) {
      console.error('Failed to execute query:', error);
      return [];
    }
  }

  async executeRun(sql: string, params: any[] = []): Promise<any> {
    if (!this.isInitialized) {
      return { lastID: 0, changes: 0 };
    }
    
    try {
      return await invoke('db_run', { sql, params });
    } catch (error) {
      console.error('Failed to execute run:', error);
      return { lastID: 0, changes: 0 };
    }
  }
}

// Export singleton instance
export const dbService = DatabaseService.getInstance();
